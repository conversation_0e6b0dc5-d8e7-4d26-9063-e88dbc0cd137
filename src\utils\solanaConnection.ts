import { Connection, clusterApiUrl } from '@solana/web3.js';

// Use environment variable or default to devnet
const SOLANA_RPC_URL = import.meta.env.VITE_SOLANA_RPC_URL || clusterApiUrl('devnet');
const SOLANA_NETWORK = import.meta.env.VITE_SOLANA_NETWORK || 'devnet';

export const connection = new Connection(SOLANA_RPC_URL, 'confirmed');

export const getExplorerUrl = (signature: string, type: 'tx' | 'address' = 'tx'): string => {
  const baseUrl = 'https://explorer.solana.com';
  const cluster = SOLANA_NETWORK === 'mainnet-beta' ? '' : `?cluster=${SOLANA_NETWORK}`;
  return `${baseUrl}/${type}/${signature}${cluster}`;
};

export const getSolanaNetwork = () => SOLANA_NETWORK;

export const formatSolAmount = (lamports: number): string => {
  return (lamports / 1e9).toFixed(4);
};
