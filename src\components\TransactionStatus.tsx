import React from 'react';
import { Check<PERSON>ircle, XCircle, Loader2, Upload, Co<PERSON>, Shield } from 'lucide-react';
import { TransactionStatus as Status } from '@/types/token';

interface TransactionStatusProps {
  status: Status;
  className?: string;
}

export const TransactionStatus: React.FC<TransactionStatusProps> = ({
  status,
  className = ''
}) => {
  const getStatusIcon = () => {
    switch (status.status) {
      case 'uploading':
        return <Upload className="w-5 h-5 text-blue-500 animate-pulse" />;
      case 'creating':
        return <Coins className="w-5 h-5 text-yellow-500 animate-pulse" />;
      case 'confirming':
        return <Shield className="w-5 h-5 text-purple-500 animate-pulse" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Loader2 className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (status.status) {
      case 'uploading':
        return 'text-blue-600';
      case 'creating':
        return 'text-yellow-600';
      case 'confirming':
        return 'text-purple-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getBgColor = () => {
    switch (status.status) {
      case 'uploading':
        return 'bg-blue-50 border-blue-200';
      case 'creating':
        return 'bg-yellow-50 border-yellow-200';
      case 'confirming':
        return 'bg-purple-50 border-purple-200';
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  if (status.status === 'idle') {
    return null;
  }

  return (
    <div className={`border rounded-lg p-4 ${getBgColor()} ${className}`}>
      <div className="flex items-center space-x-3">
        {getStatusIcon()}
        <div className="flex-1">
          <p className={`font-medium ${getStatusColor()}`}>
            {status.message}
          </p>
          {status.progress !== undefined && (
            <div className="mt-2">
              <div className="flex justify-between text-xs text-gray-600 mb-1">
                <span>Progress</span>
                <span>{status.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    status.status === 'error' 
                      ? 'bg-red-500' 
                      : status.status === 'success'
                      ? 'bg-green-500'
                      : 'bg-solana-purple'
                  }`}
                  style={{ width: `${status.progress}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
