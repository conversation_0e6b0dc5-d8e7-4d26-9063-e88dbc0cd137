// Polyfill for stream/promises module in browser environment
// This provides a basic implementation of Node.js stream promises for browser compatibility

import { Readable, Writable, Transform } from 'stream-browserify'

// Simple pipeline implementation for browser
export function pipeline(...streams: any[]): Promise<void> {
  return new Promise((resolve, reject) => {
    if (streams.length < 2) {
      reject(new Error('Pipeline requires at least 2 streams'))
      return
    }

    let current = streams[0]
    
    for (let i = 1; i < streams.length; i++) {
      const next = streams[i]
      
      current.on('error', reject)
      next.on('error', reject)
      
      if (i === streams.length - 1) {
        // Last stream
        next.on('finish', resolve)
        next.on('end', resolve)
      }
      
      current.pipe(next)
      current = next
    }
  })
}

// Simple finished implementation
export function finished(stream: any): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!stream || typeof stream.on !== 'function') {
      reject(new Error('Invalid stream'))
      return
    }

    const cleanup = () => {
      stream.removeListener('end', onEnd)
      stream.removeListener('finish', onFinish)
      stream.removeListener('error', onError)
      stream.removeListener('close', onClose)
    }

    const onEnd = () => {
      cleanup()
      resolve()
    }

    const onFinish = () => {
      cleanup()
      resolve()
    }

    const onError = (err: Error) => {
      cleanup()
      reject(err)
    }

    const onClose = () => {
      cleanup()
      resolve()
    }

    stream.on('end', onEnd)
    stream.on('finish', onFinish)
    stream.on('error', onError)
    stream.on('close', onClose)
  })
}

// Export default object to match Node.js stream/promises module structure
export default {
  pipeline,
  finished
}
