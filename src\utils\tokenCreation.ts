import {
  createAndMint,
  mplTokenMetadata,
  TokenStandard
} from '@metaplex-foundation/mpl-token-metadata';
import {
  generateSigner,
  percentAmount,
  keypairIdentity,
  createSignerFromKeypair
} from '@metaplex-foundation/umi';
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';
import { irysUploader } from '@metaplex-foundation/umi-uploader-irys';
import { base58 } from '@metaplex-foundation/umi/serializers';
import { 
  TOKEN_PROGRAM_ID, 
  AuthorityType, 
  createSetAuthorityInstruction 
} from '@solana/spl-token';
import { 
  Connection, 
  PublicKey, 
  Transaction, 
  sendAndConfirmTransaction 
} from '@solana/web3.js';
import { WalletContextState } from '@solana/wallet-adapter-react';
import { TokenCreationParams, CreatedToken, TransactionStatus } from '@/types/token';
import { connection, getExplorerUrl, getSolanaNetwork } from './solanaConnection';

// Get RPC URL from environment or use default
const getRpcUrl = (): string => {
  const network = getSolanaNetwork();
  const envRpcUrl = import.meta.env.VITE_SOLANA_RPC_URL;
  
  if (envRpcUrl) return envRpcUrl;
  
  // Default RPC URLs
  switch (network) {
    case 'mainnet-beta':
      return 'https://api.mainnet-beta.solana.com';
    case 'testnet':
      return 'https://api.testnet.solana.com';
    default:
      return 'https://api.devnet.solana.com';
  }
};

export const createToken = async (
  params: TokenCreationParams,
  metadataUri: string,
  wallet: WalletContextState,
  onStatusUpdate: (status: TransactionStatus) => void
): Promise<CreatedToken> => {
  if (!wallet.publicKey || !wallet.signTransaction) {
    throw new Error('Wallet not connected');
  }

  try {
    onStatusUpdate({
      status: 'creating',
      message: 'Setting up token creation...',
      progress: 10
    });

    // Initialize UMI
    const umi = createUmi(getRpcUrl())
      .use(mplTokenMetadata())
      .use(irysUploader());

    // Convert wallet to UMI keypair
    const walletKeypair = createSignerFromKeypair(umi, {
      publicKey: wallet.publicKey.toBytes(),
      secretKey: new Uint8Array(64) // This will be handled by the wallet adapter
    });

    umi.use(keypairIdentity(walletKeypair));

    onStatusUpdate({
      status: 'creating',
      message: 'Creating token mint...',
      progress: 30
    });

    // Generate mint signer
    const mintSigner = generateSigner(umi);

    // Calculate total supply in smallest units
    const totalSupplyInSmallestUnits = BigInt(
      params.totalSupply * Math.pow(10, params.decimals)
    );

    // Create the token
    const mintAndCreateIx = createAndMint(umi, {
      mint: mintSigner,
      name: params.name,
      symbol: params.symbol,
      uri: metadataUri,
      sellerFeeBasisPoints: percentAmount(params.sellerFeeBasisPoints || 0),
      decimals: params.decimals,
      amount: totalSupplyInSmallestUnits,
      tokenOwner: umi.identity.publicKey,
      tokenStandard: TokenStandard.Fungible,
    });

    onStatusUpdate({
      status: 'confirming',
      message: 'Sending transaction...',
      progress: 60
    });

    // Send and confirm transaction
    const tx = await mintAndCreateIx.sendAndConfirm(umi);
    const signature = base58.deserialize(tx.signature)[0];

    onStatusUpdate({
      status: 'confirming',
      message: 'Token created! Processing authorities...',
      progress: 80
    });

    const mintAddress = mintSigner.publicKey.toString();
    const explorerUrl = getExplorerUrl(signature, 'tx');
    const tokenExplorerUrl = getExplorerUrl(mintAddress, 'address');

    // Revoke authorities if requested
    if (params.revokeAuthorities) {
      await revokeTokenAuthorities(mintAddress, wallet, onStatusUpdate);
    }

    onStatusUpdate({
      status: 'success',
      message: 'Token created successfully!',
      progress: 100
    });

    return {
      mintAddress,
      transactionSignature: signature,
      metadataUri,
      explorerUrl,
      tokenExplorerUrl
    };

  } catch (error) {
    console.error('Token creation error:', error);
    onStatusUpdate({
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
    throw error;
  }
};

export const revokeTokenAuthorities = async (
  mintAddress: string,
  wallet: WalletContextState,
  onStatusUpdate: (status: TransactionStatus) => void
): Promise<string> => {
  if (!wallet.publicKey || !wallet.signTransaction) {
    throw new Error('Wallet not connected');
  }

  try {
    onStatusUpdate({
      status: 'confirming',
      message: 'Revoking mint and freeze authorities...',
      progress: 85
    });

    const mintPublicKey = new PublicKey(mintAddress);
    const transaction = new Transaction();

    // Add instruction to revoke mint authority
    transaction.add(
      createSetAuthorityInstruction(
        mintPublicKey,
        wallet.publicKey,
        AuthorityType.MintTokens,
        null
      )
    );

    // Add instruction to revoke freeze authority
    transaction.add(
      createSetAuthorityInstruction(
        mintPublicKey,
        wallet.publicKey,
        AuthorityType.FreezeAccount,
        null
      )
    );

    // Get recent blockhash
    const { blockhash } = await connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = wallet.publicKey;

    // Sign transaction
    const signedTransaction = await wallet.signTransaction(transaction);

    // Send and confirm transaction
    const signature = await connection.sendRawTransaction(signedTransaction.serialize());
    await connection.confirmTransaction(signature, 'confirmed');

    onStatusUpdate({
      status: 'confirming',
      message: 'Authorities revoked successfully!',
      progress: 95
    });

    return signature;

  } catch (error) {
    console.error('Authority revocation error:', error);
    throw new Error(`Failed to revoke authorities: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const estimateTokenCreationCost = async (): Promise<number> => {
  try {
    // Estimate based on typical token creation costs
    // This is an approximation - actual costs may vary
    const rentExemptBalance = await connection.getMinimumBalanceForRentExemption(82); // Token mint account size
    const metadataRent = await connection.getMinimumBalanceForRentExemption(679); // Metadata account size
    const transactionFee = 5000; // Approximate transaction fee in lamports
    
    return rentExemptBalance + metadataRent + transactionFee;
  } catch (error) {
    console.error('Cost estimation error:', error);
    // Return a reasonable default if estimation fails
    return 0.01 * 1e9; // 0.01 SOL in lamports
  }
};
