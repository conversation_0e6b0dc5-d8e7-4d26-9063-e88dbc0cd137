import React, { useEffect, useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { Wallet, Copy, ExternalLink } from 'lucide-react';
import { connection, formatSolAmount, getExplorerUrl } from '@/utils/solanaConnection';
import toast from 'react-hot-toast';

export const WalletConnection: React.FC = () => {
  const { publicKey, connected, disconnect } = useWallet();
  const [balance, setBalance] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (connected && publicKey) {
      fetchBalance();
    } else {
      setBalance(null);
    }
  }, [connected, publicKey]);

  const fetchBalance = async () => {
    if (!publicKey) return;
    
    setLoading(true);
    try {
      const balanceInLamports = await connection.getBalance(publicKey);
      setBalance(balanceInLamports);
    } catch (error) {
      console.error('Failed to fetch balance:', error);
      toast.error('Failed to fetch wallet balance');
    } finally {
      setLoading(false);
    }
  };

  const copyAddress = async () => {
    if (!publicKey) return;
    
    try {
      await navigator.clipboard.writeText(publicKey.toString());
      toast.success('Address copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy address');
    }
  };

  const openInExplorer = () => {
    if (!publicKey) return;
    
    const url = getExplorerUrl(publicKey.toString(), 'address');
    window.open(url, '_blank');
  };

  const formatAddress = (address: string): string => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  if (!connected) {
    return (
      <div className="card">
        <div className="flex items-center space-x-3 mb-4">
          <Wallet className="w-6 h-6 text-solana-purple" />
          <h3 className="text-lg font-semibold">Connect Wallet</h3>
        </div>
        <p className="text-gray-600 mb-4">
          Connect your Phantom wallet to create Solana SPL tokens
        </p>
        <WalletMultiButton className="!bg-solana-purple hover:!bg-purple-700" />
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <Wallet className="w-6 h-6 text-solana-green" />
          <h3 className="text-lg font-semibold">Wallet Connected</h3>
        </div>
        <button
          onClick={disconnect}
          className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
        >
          Disconnect
        </button>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div>
            <p className="text-sm text-gray-600">Address</p>
            <p className="font-mono text-sm">
              {publicKey ? formatAddress(publicKey.toString()) : ''}
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={copyAddress}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              title="Copy address"
            >
              <Copy className="w-4 h-4" />
            </button>
            <button
              onClick={openInExplorer}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              title="View in explorer"
            >
              <ExternalLink className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div>
            <p className="text-sm text-gray-600">Balance</p>
            <p className="font-semibold">
              {loading ? (
                <span className="animate-pulse">Loading...</span>
              ) : balance !== null ? (
                `${formatSolAmount(balance)} SOL`
              ) : (
                'Unknown'
              )}
            </p>
          </div>
          <button
            onClick={fetchBalance}
            disabled={loading}
            className="text-sm text-solana-purple hover:text-purple-700 transition-colors disabled:opacity-50"
          >
            Refresh
          </button>
        </div>
      </div>

      {balance !== null && balance < 0.01 * 1e9 && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            ⚠️ Low balance detected. You may need more SOL to create tokens.
          </p>
        </div>
      )}
    </div>
  );
};
