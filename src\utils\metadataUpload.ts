import { TokenMetadata, UploadedMetadata } from '@/types/token';

// This is a simplified version - in production, you'd want to use a proper IPFS service
// like Pinata, NFT.Storage, or Arweave
export const uploadToIPFS = async (file: File): Promise<string> => {
  // For demo purposes, we'll create a data URL
  // In production, replace this with actual IPFS upload
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = () => {
      // This is just for demo - replace with actual IPFS upload
      const dataUrl = reader.result as string;
      resolve(dataUrl);
    };
    reader.readAsDataURL(file);
  });
};

export const uploadMetadata = async (
  metadata: TokenMetadata,
  imageFile?: File
): Promise<UploadedMetadata> => {
  let imageUri: string | undefined;
  
  // Upload image if provided
  if (imageFile) {
    imageUri = await uploadToIPFS(imageFile);
  }
  
  // Create metadata object
  const metadataObject = {
    ...metadata,
    image: imageUri || metadata.image,
  };
  
  // In production, upload metadata JSON to IPFS
  // For demo, we'll create a data URL
  const metadataJson = JSON.stringify(metadataObject, null, 2);
  const metadataBlob = new Blob([metadataJson], { type: 'application/json' });
  const metadataFile = new File([metadataBlob], 'metadata.json', { type: 'application/json' });
  
  const metadataUri = await uploadToIPFS(metadataFile);
  
  return {
    metadataUri,
    imageUri,
  };
};

// Production IPFS upload function template
export const uploadToPinata = async (file: File, apiKey: string, secretKey: string): Promise<string> => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('https://api.pinata.cloud/pinning/pinFileToIPFS', {
    method: 'POST',
    headers: {
      'pinata_api_key': apiKey,
      'pinata_secret_api_key': secretKey,
    },
    body: formData,
  });
  
  if (!response.ok) {
    throw new Error('Failed to upload to Pinata');
  }
  
  const result = await response.json();
  return `https://gateway.pinata.cloud/ipfs/${result.IpfsHash}`;
};

// Validate image file
export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  const validTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  if (!validTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Please upload a PNG, JPG, JPEG, or GIF image.',
    };
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Image size must be less than 5MB.',
    };
  }
  
  return { valid: true };
};
