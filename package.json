{"name": "solana-token-creator", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "keywords": ["solana", "spl-token", "cryptocurrency", "blockchain"], "author": "", "license": "ISC", "description": "A comprehensive web application for creating Solana SPL tokens with Phantom wallet integration", "dependencies": {"@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@metaplex-foundation/mpl-toolbox": "^0.10.0", "@metaplex-foundation/umi": "^1.2.0", "@metaplex-foundation/umi-bundle-defaults": "^1.2.0", "@metaplex-foundation/umi-uploader-irys": "^1.2.0", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-phantom": "^0.9.24", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/wallet-adapter-wallets": "^0.19.32", "@solana/web3.js": "^1.98.2", "assert": "^2.1.0", "bn.js": "^5.2.2", "browserify-fs": "^1.0.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "clsx": "^2.0.0", "crypto-browserify": "^3.12.1", "https-browserify": "^1.0.0", "lucide-react": "^0.263.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "readable-stream": "^4.7.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5"}, "devDependencies": {"@types/node": "^20.5.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}