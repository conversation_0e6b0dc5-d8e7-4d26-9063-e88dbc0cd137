@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn-primary {
    @apply bg-solana-purple hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-outline {
    @apply border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-solana-purple focus:border-transparent transition-all duration-200;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #9945FF 0%, #14F195 100%);
  }
  
  .glass-effect {
    @apply backdrop-blur-sm bg-white/80 border border-white/20;
  }
}

@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #9945FF 0%, #14F195 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(153, 69, 255, 0.3);
  }
}

/* Wallet adapter styles */
.wallet-adapter-button {
  @apply btn-primary !important;
}

.wallet-adapter-button-trigger {
  @apply btn-primary !important;
}

.wallet-adapter-modal-wrapper {
  @apply !bg-black/50 !backdrop-blur-sm;
}

.wallet-adapter-modal {
  @apply !bg-white !rounded-xl !border !border-gray-200;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
