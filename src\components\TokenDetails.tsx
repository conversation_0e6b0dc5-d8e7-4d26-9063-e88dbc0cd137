import React from 'react';
import { ExternalLink, Copy, Coins, FileText } from 'lucide-react';
import { CreatedToken } from '@/types/token';
import toast from 'react-hot-toast';

interface TokenDetailsProps {
  token: CreatedToken;
  className?: string;
}

export const TokenDetails: React.FC<TokenDetailsProps> = ({
  token,
  className = ''
}) => {
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${label} copied to clipboard!`);
    } catch (error) {
      toast.error(`Failed to copy ${label.toLowerCase()}`);
    }
  };

  const formatAddress = (address: string): string => {
    return `${address.slice(0, 8)}...${address.slice(-8)}`;
  };

  return (
    <div className={`card ${className}`}>
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-green-100 rounded-lg">
          <Coins className="w-6 h-6 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-green-600">Token Created Successfully!</h3>
          <p className="text-sm text-gray-600">Your SPL token has been deployed to Solana</p>
        </div>
      </div>

      <div className="space-y-4">
        {/* Mint Address */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-700">Mint Address</label>
            <div className="flex space-x-2">
              <button
                onClick={() => copyToClipboard(token.mintAddress, 'Mint address')}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title="Copy mint address"
              >
                <Copy className="w-4 h-4" />
              </button>
              <button
                onClick={() => window.open(token.tokenExplorerUrl, '_blank')}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title="View token in explorer"
              >
                <ExternalLink className="w-4 h-4" />
              </button>
            </div>
          </div>
          <p className="font-mono text-sm text-gray-900 break-all">
            {token.mintAddress}
          </p>
        </div>

        {/* Transaction Signature */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-700">Transaction Signature</label>
            <div className="flex space-x-2">
              <button
                onClick={() => copyToClipboard(token.transactionSignature, 'Transaction signature')}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title="Copy transaction signature"
              >
                <Copy className="w-4 h-4" />
              </button>
              <button
                onClick={() => window.open(token.explorerUrl, '_blank')}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title="View transaction in explorer"
              >
                <ExternalLink className="w-4 h-4" />
              </button>
            </div>
          </div>
          <p className="font-mono text-sm text-gray-900 break-all">
            {token.transactionSignature}
          </p>
        </div>

        {/* Metadata URI */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-700">Metadata URI</label>
            <div className="flex space-x-2">
              <button
                onClick={() => copyToClipboard(token.metadataUri, 'Metadata URI')}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title="Copy metadata URI"
              >
                <Copy className="w-4 h-4" />
              </button>
              <button
                onClick={() => window.open(token.metadataUri, '_blank')}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title="View metadata"
              >
                <FileText className="w-4 h-4" />
              </button>
            </div>
          </div>
          <p className="font-mono text-sm text-gray-900 break-all">
            {formatAddress(token.metadataUri)}
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-6 flex flex-col sm:flex-row gap-3">
        <button
          onClick={() => window.open(token.tokenExplorerUrl, '_blank')}
          className="flex-1 btn-primary flex items-center justify-center space-x-2"
        >
          <ExternalLink className="w-4 h-4" />
          <span>View Token</span>
        </button>
        <button
          onClick={() => window.open(token.explorerUrl, '_blank')}
          className="flex-1 btn-outline flex items-center justify-center space-x-2"
        >
          <ExternalLink className="w-4 h-4" />
          <span>View Transaction</span>
        </button>
      </div>

      {/* Important Notes */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Important Notes:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Save your mint address - you'll need it to manage your token</li>
          <li>• Your token is now live on the Solana blockchain</li>
          <li>• Share the mint address with others to let them find your token</li>
          <li>• Consider adding liquidity to DEXs for trading</li>
        </ul>
      </div>
    </div>
  );
};
