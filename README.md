# Solana Token Creator

A comprehensive web application for creating Solana SPL tokens with Phantom wallet integration, built with React, TypeScript, and Tailwind CSS.

## Features

- 🔗 **Phantom Wallet Integration** - Connect your wallet securely
- 🎨 **Custom Token Images** - Upload images stored on IPFS
- 📊 **Real-time Balance Display** - See your SOL balance and transaction costs
- ⚙️ **Authority Management** - Option to revoke mint and freeze authorities
- 📱 **Responsive Design** - Works on desktop and mobile
- 🔍 **Explorer Integration** - Direct links to Solana Explorer
- 💰 **Cost Estimation** - See estimated transaction costs before creation
- 🎯 **Step-by-step Wizard** - Intuitive token creation process

## Prerequisites

- Node.js 18+ and npm/yarn
- Phantom wallet browser extension
- SOL tokens for transaction fees (devnet SOL for testing)

## Quick Start

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd solana-token-creator
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your preferred settings
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to `http://localhost:3000`

## Environment Configuration

Create a `.env` file based on `.env.example`:

```env
# Network (devnet, testnet, mainnet-beta)
VITE_SOLANA_NETWORK=devnet

# Custom RPC URL (optional)
VITE_SOLANA_RPC_URL=https://api.devnet.solana.com

# IPFS Configuration (for production)
VITE_PINATA_API_KEY=your_pinata_api_key
VITE_PINATA_SECRET_KEY=your_pinata_secret_key
```

## Usage

1. **Connect Wallet**: Click "Connect Wallet" and approve the connection in Phantom
2. **Token Information**: Enter your token's name, symbol, description, and upload an image
3. **Token Settings**: Configure supply, decimals, and authority settings
4. **Create Token**: Review costs and create your token
5. **View Results**: Get your mint address and explorer links

## Network Support

- **Devnet** (default) - For testing with free SOL
- **Testnet** - Additional testing environment
- **Mainnet** - Production deployment (requires real SOL)

## Technology Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Blockchain**: Solana Web3.js, Metaplex Foundation libraries
- **Wallet**: Solana Wallet Adapter
- **Build Tool**: Vite
- **Storage**: IPFS (via Pinata or similar)

## Project Structure

```
src/
├── components/          # React components
│   ├── WalletConnection.tsx
│   ├── TokenCreationWizard.tsx
│   ├── ImageUpload.tsx
│   ├── TransactionStatus.tsx
│   └── TokenDetails.tsx
├── utils/              # Utility functions
│   ├── tokenCreation.ts
│   ├── metadataUpload.ts
│   └── solanaConnection.ts
├── types/              # TypeScript definitions
│   └── token.ts
├── App.tsx             # Main app component
├── main.tsx           # Entry point
└── index.css          # Global styles
```

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Security Notes

⚠️ **Important Security Considerations:**

- Never share your wallet private keys
- Always verify transaction details before signing
- Use devnet for testing before mainnet deployment
- This tool is for educational purposes - audit before production use
- Keep your wallet files secure and never commit them to version control

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Check the GitHub issues
- Review Solana documentation
- Join the Solana Discord community

---

**Disclaimer**: This tool is provided as-is for educational purposes. Always verify smart contracts and do your own research before deploying to mainnet.
