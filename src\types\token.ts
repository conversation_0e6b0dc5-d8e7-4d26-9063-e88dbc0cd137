export interface TokenMetadata {
  name: string;
  symbol: string;
  description: string;
  image?: string;
  external_url?: string;
  creator?: {
    name: string;
    site?: string;
  };
  twitter?: string;
  telegram?: string;
  discord?: string;
}

export interface TokenCreationParams {
  name: string;
  symbol: string;
  description: string;
  totalSupply: number;
  decimals: number;
  imageFile?: File;
  revokeAuthorities: boolean;
  sellerFeeBasisPoints?: number;
}

export interface CreatedToken {
  mintAddress: string;
  transactionSignature: string;
  metadataUri: string;
  explorerUrl: string;
  tokenExplorerUrl: string;
}

export interface TransactionStatus {
  status: 'idle' | 'uploading' | 'creating' | 'confirming' | 'success' | 'error';
  message: string;
  progress?: number;
}

export interface UploadedMetadata {
  metadataUri: string;
  imageUri?: string;
}
