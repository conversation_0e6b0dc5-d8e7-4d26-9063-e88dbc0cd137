import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { Coins, DollarSign, Settings, Shield } from 'lucide-react';
import { TokenCreationParams, TransactionStatus, CreatedToken } from '@/types/token';
import { ImageUpload } from './ImageUpload';
import { TransactionStatus as TransactionStatusComponent } from './TransactionStatus';
import { TokenDetails } from './TokenDetails';
import { createToken, estimateTokenCreationCost } from '@/utils/tokenCreation';
import { uploadMetadata } from '@/utils/metadataUpload';
import { formatSolAmount } from '@/utils/solanaConnection';
import toast from 'react-hot-toast';

export const TokenCreationWizard: React.FC = () => {
  const wallet = useWallet();
  const [step, setStep] = useState(1);
  const [estimatedCost, setEstimatedCost] = useState<number | null>(null);
  const [transactionStatus, setTransactionStatus] = useState<TransactionStatus>({
    status: 'idle',
    message: ''
  });
  const [createdToken, setCreatedToken] = useState<CreatedToken | null>(null);
  
  const [formData, setFormData] = useState<TokenCreationParams>({
    name: '',
    symbol: '',
    description: '',
    totalSupply: 1000000,
    decimals: 9,
    revokeAuthorities: true,
    sellerFeeBasisPoints: 0
  });
  
  const [imageFile, setImageFile] = useState<File | null>(null);

  useEffect(() => {
    estimateTokenCreationCost().then(setEstimatedCost);
  }, []);

  const handleInputChange = (field: keyof TokenCreationParams, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      toast.error('Token name is required');
      return false;
    }
    if (!formData.symbol.trim()) {
      toast.error('Token symbol is required');
      return false;
    }
    if (formData.symbol.length > 10) {
      toast.error('Token symbol must be 10 characters or less');
      return false;
    }
    if (!formData.description.trim()) {
      toast.error('Token description is required');
      return false;
    }
    if (formData.totalSupply <= 0) {
      toast.error('Total supply must be greater than 0');
      return false;
    }
    if (formData.decimals < 0 || formData.decimals > 9) {
      toast.error('Decimals must be between 0 and 9');
      return false;
    }
    return true;
  };

  const handleCreateToken = async () => {
    if (!wallet.connected || !wallet.publicKey) {
      toast.error('Please connect your wallet first');
      return;
    }

    if (!validateForm()) return;

    try {
      setTransactionStatus({
        status: 'uploading',
        message: 'Uploading metadata to IPFS...',
        progress: 5
      });

      // Upload metadata
      const metadata = {
        name: formData.name,
        symbol: formData.symbol,
        description: formData.description,
        creator: {
          name: 'Token Creator',
          site: window.location.origin
        }
      };

      const uploadedMetadata = await uploadMetadata(metadata, imageFile || undefined);

      // Create token
      const token = await createToken(
        formData,
        uploadedMetadata.metadataUri,
        wallet,
        setTransactionStatus
      );

      setCreatedToken(token);
      setStep(3);
      toast.success('Token created successfully!');

    } catch (error) {
      console.error('Token creation failed:', error);
      setTransactionStatus({
        status: 'error',
        message: error instanceof Error ? error.message : 'Token creation failed'
      });
    }
  };

  const resetWizard = () => {
    setStep(1);
    setFormData({
      name: '',
      symbol: '',
      description: '',
      totalSupply: 1000000,
      decimals: 9,
      revokeAuthorities: true,
      sellerFeeBasisPoints: 0
    });
    setImageFile(null);
    setTransactionStatus({ status: 'idle', message: '' });
    setCreatedToken(null);
  };

  if (!wallet.connected) {
    return (
      <div className="card text-center">
        <Coins className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Connect Wallet to Continue</h3>
        <p className="text-gray-600">
          Please connect your Phantom wallet to start creating tokens
        </p>
      </div>
    );
  }

  if (step === 3 && createdToken) {
    return (
      <div className="space-y-6">
        <TokenDetails token={createdToken} />
        <div className="text-center">
          <button
            onClick={resetWizard}
            className="btn-primary"
          >
            Create Another Token
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        {[1, 2].map((stepNum) => (
          <div key={stepNum} className="flex items-center">
            <div
              className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${step >= stepNum 
                  ? 'bg-solana-purple text-white' 
                  : 'bg-gray-200 text-gray-600'
                }
              `}
            >
              {stepNum}
            </div>
            {stepNum < 2 && (
              <div
                className={`
                  w-16 h-1 mx-2
                  ${step > stepNum ? 'bg-solana-purple' : 'bg-gray-200'}
                `}
              />
            )}
          </div>
        ))}
      </div>

      {step === 1 && (
        <div className="card">
          <div className="flex items-center space-x-3 mb-6">
            <Coins className="w-6 h-6 text-solana-purple" />
            <h3 className="text-lg font-semibold">Token Information</h3>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Token Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="e.g., My Awesome Token"
                  className="input-field"
                  maxLength={32}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Token Symbol *
                </label>
                <input
                  type="text"
                  value={formData.symbol}
                  onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
                  placeholder="e.g., MAT"
                  className="input-field"
                  maxLength={10}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your token's purpose and utility..."
                className="input-field h-24 resize-none"
                maxLength={500}
              />
            </div>

            <ImageUpload
              onImageSelect={setImageFile}
              currentImage={imageFile}
            />

            <div className="flex justify-end">
              <button
                onClick={() => setStep(2)}
                disabled={!formData.name || !formData.symbol || !formData.description}
                className="btn-primary"
              >
                Next: Token Settings
              </button>
            </div>
          </div>
        </div>
      )}

      {step === 2 && (
        <div className="space-y-6">
          <div className="card">
            <div className="flex items-center space-x-3 mb-6">
              <Settings className="w-6 h-6 text-solana-purple" />
              <h3 className="text-lg font-semibold">Token Settings</h3>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Total Supply *
                  </label>
                  <input
                    type="number"
                    value={formData.totalSupply}
                    onChange={(e) => handleInputChange('totalSupply', Number(e.target.value))}
                    min="1"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Decimals *
                  </label>
                  <select
                    value={formData.decimals}
                    onChange={(e) => handleInputChange('decimals', Number(e.target.value))}
                    className="input-field"
                  >
                    {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9].map(num => (
                      <option key={num} value={num}>{num}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
                <Shield className="w-5 h-5 text-green-600" />
                <div className="flex-1">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.revokeAuthorities}
                      onChange={(e) => handleInputChange('revokeAuthorities', e.target.checked)}
                      className="rounded border-gray-300 text-solana-purple focus:ring-solana-purple"
                    />
                    <span className="text-sm font-medium">Revoke mint and freeze authorities</span>
                  </label>
                  <p className="text-xs text-gray-600 mt-1">
                    Recommended: Makes your token immutable and prevents future minting
                  </p>
                </div>
              </div>

              {estimatedCost && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <DollarSign className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Estimated Cost</span>
                  </div>
                  <p className="text-sm text-blue-800">
                    ~{formatSolAmount(estimatedCost)} SOL
                  </p>
                </div>
              )}
            </div>

            <div className="flex justify-between mt-6">
              <button
                onClick={() => setStep(1)}
                className="btn-outline"
              >
                Back
              </button>
              <button
                onClick={handleCreateToken}
                disabled={transactionStatus.status !== 'idle'}
                className="btn-primary"
              >
                Create Token
              </button>
            </div>
          </div>

          <TransactionStatusComponent status={transactionStatus} />
        </div>
      )}
    </div>
  );
};
