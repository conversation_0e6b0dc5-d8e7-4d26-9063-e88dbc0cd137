import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      // Node.js polyfills for browser compatibility
      'stream': 'stream-browserify',
      'buffer': 'buffer',
      'crypto': 'crypto-browserify',
      'assert': 'assert',
      'http': 'stream-http',
      'https': 'https-browserify',
      'os': 'os-browserify/browser',
      'url': 'url',
      'zlib': 'browserify-zlib',
      'path': 'path-browserify',
      'fs': 'browserify-fs',
    },
  },
  define: {
    global: 'globalThis',
    // Define process.env for browser compatibility
    'process.env': {},
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    target: 'esnext',
    rollupOptions: {
      external: [],
    },
  },
  optimizeDeps: {
    include: [
      '@solana/web3.js',
      '@solana/wallet-adapter-base',
      '@solana/wallet-adapter-phantom',
      '@solana/wallet-adapter-react',
      '@solana/wallet-adapter-react-ui',
      '@solana/wallet-adapter-wallets',
      '@metaplex-foundation/umi',
      '@metaplex-foundation/umi-bundle-defaults',
      '@metaplex-foundation/mpl-token-metadata',
      // Include polyfills in optimization
      'buffer',
      'stream-browserify',
      'crypto-browserify',
      'assert',
      'util',
    ],
  },
})
