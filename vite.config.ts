import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: [
      { find: '@', replacement: resolve(__dirname, './src') },
      // Node.js polyfills for browser compatibility
      { find: /^stream$/, replacement: 'stream-browserify' },
      { find: /^stream\/promises$/, replacement: resolve(__dirname, './src/polyfills/stream-promises.ts') },
      { find: /^buffer$/, replacement: 'buffer' },
      { find: /^crypto$/, replacement: 'crypto-browserify' },
      { find: /^assert$/, replacement: 'assert' },
      { find: /^http$/, replacement: 'stream-http' },
      { find: /^https$/, replacement: 'https-browserify' },
      { find: /^os$/, replacement: 'os-browserify/browser' },
      { find: /^url$/, replacement: 'url' },
      { find: /^zlib$/, replacement: 'browserify-zlib' },
      { find: /^path$/, replacement: 'path-browserify' },
      { find: /^fs$/, replacement: 'browserify-fs' },
    ],
  },
  define: {
    global: 'globalThis',
    // Define process.env for browser compatibility
    'process.env': {},
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    target: 'esnext',
    rollupOptions: {
      external: [],
    },
  },
  optimizeDeps: {
    include: [
      '@solana/web3.js',
      '@solana/wallet-adapter-base',
      '@solana/wallet-adapter-phantom',
      '@solana/wallet-adapter-react',
      '@solana/wallet-adapter-react-ui',
      '@solana/wallet-adapter-wallets',
      '@metaplex-foundation/umi',
      '@metaplex-foundation/umi-bundle-defaults',
      '@metaplex-foundation/mpl-token-metadata',
      // Include polyfills in optimization
      'buffer',
      'stream-browserify',
      'crypto-browserify',
      'assert',
      'util',
    ],
  },
})
