// Polyfills for Node.js modules in browser environment
// This file ensures that Node.js core modules work properly in the browser

// Buffer polyfill - required for many crypto operations
import { <PERSON><PERSON><PERSON> } from 'buffer'

// Process polyfill - required for environment variables and process info
import process from 'process'

// Make Buffer and process available globally
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.Buffer = Buffer
  // @ts-ignore
  window.process = process
  // @ts-ignore
  window.global = window
}

// Make them available globally for Node.js-style imports
globalThis.Buffer = Buffer
globalThis.process = process

// Set up process.env if it doesn't exist
if (!process.env) {
  process.env = {}
}

// Set NODE_ENV if not already set
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development'
}

// Export for explicit imports if needed
export { Buffer, process }
