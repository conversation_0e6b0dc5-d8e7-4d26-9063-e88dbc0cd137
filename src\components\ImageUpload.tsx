import React, { useCallback, useState } from 'react';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { validateImageFile } from '@/utils/metadataUpload';
import toast from 'react-hot-toast';

interface ImageUploadProps {
  onImageSelect: (file: File | null) => void;
  currentImage?: File | null;
  disabled?: boolean;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageSelect,
  currentImage,
  disabled = false
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);

  React.useEffect(() => {
    if (currentImage) {
      const reader = new FileReader();
      reader.onload = () => setPreview(reader.result as string);
      reader.readAsDataURL(currentImage);
    } else {
      setPreview(null);
    }
  }, [currentImage]);

  const handleFile = useCallback((file: File) => {
    const validation = validateImageFile(file);
    
    if (!validation.valid) {
      toast.error(validation.error || 'Invalid file');
      return;
    }

    onImageSelect(file);
    toast.success('Image uploaded successfully!');
  }, [onImageSelect]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  }, [handleFile, disabled]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;
    
    const files = e.target.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  }, [handleFile, disabled]);

  const removeImage = useCallback(() => {
    onImageSelect(null);
    setPreview(null);
  }, [onImageSelect]);

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700">
        Token Logo/Image
      </label>
      
      {preview ? (
        <div className="relative">
          <div className="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={preview}
              alt="Token preview"
              className="w-full h-full object-cover"
            />
            {!disabled && (
              <button
                onClick={removeImage}
                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                title="Remove image"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
          <div className="mt-2 text-sm text-gray-600">
            <p className="font-medium">{currentImage?.name}</p>
            <p>{currentImage ? `${(currentImage.size / 1024 / 1024).toFixed(2)} MB` : ''}</p>
          </div>
        </div>
      ) : (
        <div
          className={`
            relative border-2 border-dashed rounded-lg p-6 transition-colors
            ${dragActive 
              ? 'border-solana-purple bg-purple-50' 
              : 'border-gray-300 hover:border-gray-400'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={() => !disabled && document.getElementById('image-upload')?.click()}
        >
          <input
            id="image-upload"
            type="file"
            accept="image/png,image/jpeg,image/jpg,image/gif"
            onChange={handleFileInput}
            className="hidden"
            disabled={disabled}
          />
          
          <div className="text-center">
            <div className="mx-auto w-12 h-12 text-gray-400 mb-4">
              {dragActive ? (
                <Upload className="w-full h-full" />
              ) : (
                <ImageIcon className="w-full h-full" />
              )}
            </div>
            
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-900">
                {dragActive ? 'Drop image here' : 'Upload token image'}
              </p>
              <p className="text-xs text-gray-500">
                PNG, JPG, JPEG, or GIF up to 5MB
              </p>
              <p className="text-xs text-gray-400">
                Drag and drop or click to browse
              </p>
            </div>
          </div>
        </div>
      )}
      
      <p className="text-xs text-gray-500">
        Optional: Add an image to represent your token. This will be stored on IPFS and associated with your token's metadata.
      </p>
    </div>
  );
};
